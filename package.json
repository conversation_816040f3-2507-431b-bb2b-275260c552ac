{"name": "<PERSON>ai-lyrics", "version": "1.2.1", "description": "This extension enhances your Spotify experience by providing line-by-line translations, along with <PERSON><PERSON><PERSON> and <PERSON><PERSON> for Japanese lyrics, and Romanization for Korean lyrics.", "private": false, "author": "hudzax", "scripts": {"build": "spicetify-creator", "build-local": "spicetify-creator --out=dist", "watch": "spicetify-creator --watch", "spicetify-watch": "bash spicetify-watch.sh"}, "license": "MIT", "devDependencies": {"@eslint/js": "^9.28.0", "@types/node": "^22.14.0", "@types/pako": "^2.0.3", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/spark-md5": "^3.0.5", "eslint": "^9.28.0", "globals": "^16.2.0", "spicetify-creator": "^1.0.17", "typescript-eslint": "^8.33.1"}, "dependencies": {"@google/genai": "^0.8.0", "@hudzax/web-modules": "npm:@jsr/hudzax__web-modules@^1.0.5", "pako": "^2.1.0", "simplebar": "^6.3.0", "spark-md5": "^3.0.2"}}