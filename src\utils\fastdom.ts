/**
 * Optimized Custom FastDOM implementation
 *
 * This module provides a simple interface to batch DOM read/write operations
 * which helps prevent layout thrashing by properly sequencing DOM operations.
 *
 * Optimizations include:
 * - Index-based queue processing for better performance
 * - Consistent frame scheduling
 * - Improved error handling with context
 * - Better memory management
 * - Support for cancellation of pending operations
 */

// Queues for batching operations
const readQueue: Array<{
  fn: () => unknown;
  callback: (result: unknown) => void;
  id: number;
}> = [];
const writeQueue: Array<{
  fn: () => unknown;
  callback: (result: unknown) => void;
  id: number;
}> = [];

// Frame tracking
let scheduledAnimationFrame = false;
let currentQueueId = 0;
let isProcessing = false;

// Performance monitoring
let lastProcessTime = 0;
const MAX_PROCESS_TIME = 8; // Max time (ms) to spend processing per frame

/**
 * Process all queued operations in the correct order
 */
function processQueues() {
  isProcessing = true;
  const startTime = performance.now();

  // Process all read operations first
  let readIndex = 0;
  while (readIndex < readQueue.length) {
    const { fn, callback } = readQueue[readIndex];
    try {
      const result = fn();
      callback(result);
    } catch (error) {
      console.error('Error in read operation:', error);
      callback(null);
    }
    readIndex++;
  }

  // Clear processed read operations
  readQueue.splice(0, readIndex);

  // Then process write operations
  if (writeQueue.length) {
    // Use requestAnimationFrame for writes to ensure they happen in the next frame
    requestAnimationFrame(() => {
      let writeIndex = 0;
      const writeStartTime = performance.now();

      while (writeIndex < writeQueue.length) {
        // Check if we've exceeded our time budget
        if (performance.now() - writeStartTime > MAX_PROCESS_TIME) {
          // If we've run out of time, schedule another frame
          requestAnimationFrame(processQueues);
          break;
        }

        const { fn, callback } = writeQueue[writeIndex];
        try {
          const result = fn();
          callback(result);
        } catch (error) {
          console.error('Error in write operation:', error);
          callback(null);
        }
        writeIndex++;
      }

      // Clear processed write operations
      writeQueue.splice(0, writeIndex);

      scheduledAnimationFrame = false;
      isProcessing = false;

      // Schedule another frame if there are still operations pending
      if (readQueue.length > 0 || writeQueue.length > 0) {
        scheduleFrame();
      }
    });
  } else {
    scheduledAnimationFrame = false;
    isProcessing = false;

    // Schedule another frame if there are still read operations pending
    if (readQueue.length > 0) {
      scheduleFrame();
    }
  }

  // Track processing time for monitoring
  lastProcessTime = performance.now() - startTime;
}

/**
 * Schedule a frame to process queues if not already scheduled
 */
function scheduleFrame() {
  if (!scheduledAnimationFrame && (readQueue.length > 0 || writeQueue.length > 0)) {
    scheduledAnimationFrame = true;
    requestAnimationFrame(processQueues);
  }
}

/**
 * Queue a read operation
 */
function measure<T>(callback: (result: T) => void, fn: () => T) {
  const id = currentQueueId++;
  readQueue.push({ fn, callback, id });
  scheduleFrame();
  return id;
}

/**
 * Queue a write operation
 */
function mutate<T>(callback: (result: T) => void, fn: () => T) {
  const id = currentQueueId++;
  writeQueue.push({ fn, callback, id });
  scheduleFrame();
  return id;
}

/**
 * Clear all queued operations
 */
function clear() {
  readQueue.length = 0;
  writeQueue.length = 0;
}

/**
 * Cancel a specific operation by ID
 */
function cancel(id: number) {
  // Find and remove the operation from the read queue
  const readIndex = readQueue.findIndex((op) => op.id === id);
  if (readIndex !== -1) {
    readQueue.splice(readIndex, 1);
    return true;
  }

  // Find and remove the operation from the write queue
  const writeIndex = writeQueue.findIndex((op) => op.id === id);
  if (writeIndex !== -1) {
    writeQueue.splice(writeIndex, 1);
    return true;
  }

  return false;
}

// Export the FastDOM-like API
export default {
  /**
   * Schedule a DOM read operation
   * Use for operations that read from the DOM (getBoundingClientRect, offsetHeight, etc.)
   *
   * @param fn Function that performs DOM read operations
   * @returns Promise that resolves with the result of fn
   */
  read: <T>(fn: () => T): Promise<T> => {
    return new Promise((resolve) => {
      measure(resolve, fn);
    });
  },

  /**
   * Schedule a DOM write operation
   * Use for operations that write to the DOM (classList, style, appendChild, etc.)
   *
   * @param fn Function that performs DOM write operations
   * @returns Promise that resolves when the operation is complete
   */
  write: <T>(fn: () => T): Promise<T> => {
    return new Promise((resolve) => {
      mutate(resolve, fn);
    });
  },

  /**
   * Schedule a DOM read operation followed immediately by a write operation
   * Useful for read-then-write patterns (measure then update)
   *
   * @param readFn Function that performs DOM read operations
   * @param writeFn Function that performs DOM write operations using read results
   * @returns Promise that resolves when both operations are complete
   */
  readThenWrite: <R, W>(readFn: () => R, writeFn: (readResult: R) => W): Promise<W> => {
    return new Promise((resolve) => {
      const readId = measure((readResult: R) => {
        mutate(resolve, () => writeFn(readResult as R));
      }, readFn);

      // Return a function to cancel the operation
      return {
        cancel: () => {
          cancel(readId);
        },
      };
    });
  },

  /**
   * Clear all scheduled tasks
   * Useful when a component is unmounting or when you need to cancel pending operations
   */
  clear,

  /**
   * Get performance metrics
   * @returns Object with performance metrics
   */
  getMetrics: () => ({
    readQueueLength: readQueue.length,
    writeQueueLength: writeQueue.length,
    lastProcessTime,
    isProcessing,
    scheduledAnimationFrame,
  }),
};
